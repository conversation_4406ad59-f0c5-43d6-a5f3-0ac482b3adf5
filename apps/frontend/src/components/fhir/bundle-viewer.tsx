"use client";

import { useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { AlertTriangle } from "lucide-react";
import { BundleHeader } from "./bundle-header";
// import { ResourceSummary } from "./resource-summary";
import { ResourceGroupDisplay } from "./resource-group-display";
import { DocumentReferenceViewer } from "./document-reference-viewer";
import {
  parseAllBundleEntries,
  type ParsedFhirData,
} from "@/lib/fhir/bundle-parser";
import { sortResourcesByPriority } from "@/lib/fhir/resource-config";

interface BundleViewerProps {
  bundle: any;
  currentIndex: number;
  totalBundles: number;
}

export function BundleViewer({
  bundle,
  currentIndex,
  totalBundles,
}: BundleViewerProps) {
  const parsedData: ParsedFhirData | null = useMemo(() => {
    try {
      return parseAllBundleEntries(bundle.bundleJson);
    } catch (error) {
      console.error("Error parsing bundle:", error);
      return null;
    }
  }, [bundle.bundleJson]);

  if (!parsedData) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <div>
              <p className="text-sm font-medium text-red-800">
                Failed to parse FHIR bundle
              </p>
              <p className="text-sm text-red-700">
                The bundle structure appears to be invalid or corrupted.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Sort resources by priority for display
  const sortedResourceGroups = sortResourcesByPriority(
    Object.entries(parsedData.resourceGroups).map(([resourceType, group]) => ({
      resourceType,
      data: group.resources,
    })),
  );

  const hasDocuments = parsedData.documents.totalDocuments > 0;

  return (
    <div className="space-y-6">
      {/* Bundle Header */}
      <BundleHeader
        bundleType={parsedData.bundleType}
        bundleId={parsedData.bundleId}
        timestamp={parsedData.timestamp}
        totalEntries={parsedData.totalEntries}
        currentIndex={currentIndex}
        totalBundles={totalBundles}
      />

      {/* Resource Summary */}
      {/* <ResourceSummary
        summary={parsedData.summary}
        totalEntries={parsedData.totalEntries}
      /> */}

      {/* Resources Display - Single View */}
      <div className="space-y-4">
        {sortedResourceGroups.map(({ resourceType, data, config }) => (
          <ResourceGroupDisplay
            key={resourceType}
            resourceType={resourceType}
            resources={data}
            config={config}
            defaultExpanded={true}
          />
        ))}

        {/* Documents Section - Integrated into resources */}
        {hasDocuments && (
          <DocumentReferenceViewer
            documentReferences={parsedData.documents.documentReferences}
            binaries={parsedData.documents.binaries}
          />
        )}
      </div>
    </div>
  );
}
