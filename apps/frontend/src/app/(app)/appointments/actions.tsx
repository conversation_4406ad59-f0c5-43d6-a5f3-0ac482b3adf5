"use server";

import { db } from "@/lib/db";
import { getCurrentUser } from "@/lib/session";
import { cookies } from "next/headers";
import { revalidatePath } from "next/cache";

// Types
export interface Appointment {
  id: string;
  patientId: string;
  doctorId: string;
  branchId: string;
  organizationId: string;
  appointmentDate: Date;
  startTime: string;
  endTime: string;
  duration: number;
  status: AppointmentStatus;
  type: AppointmentType;
  notes?: string;
  cancellationReason?: string;
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    phone: string;
    email?: string;
    dateOfBirth: Date;
    gender: string;
    allergies?: string;
  };
  doctor: {
    id: string;
    user: {
      id: string;
      name: string;
      email: string;
    };
    specialization: string;
    qualification: string;
    consultationFee?: number;
  };
  branch: {
    id: string;
    name: string;
  };
  queueStatus?: QueueStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface QueueStatus {
  id: string;
  queueNumber: number;
  status: QueueStatusType;
  estimatedStartTime?: Date;
  actualStartTime?: Date;
  completionTime?: Date;
  pauseReason?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export type AppointmentStatus = 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
export type AppointmentType = 'consultation' | 'follow-up' | 'emergency' | 'routine';
export type QueueStatusType = 'waiting' | 'in-consultation' | 'paused' | 'completed' | 'cancelled';

export interface AppointmentFilters {
  doctorId?: string;
  patientId?: string;
  branchId?: string;
  status?: string;
  date?: string;
  startDate?: string;
  endDate?: string;
  forConsultation?: boolean;
  withoutConsultation?: boolean;
}

export interface CreateAppointmentData {
  patientId: string;
  doctorId: string;
  branchId: string;
  appointmentDate: string;
  startTime: string;
  endTime: string;
  duration: number;
  type: AppointmentType;
  notes?: string;
}

export interface UpdateAppointmentData {
  patientId?: string;
  doctorId?: string;
  branchId?: string;
  appointmentDate?: string;
  startTime?: string;
  endTime?: string;
  duration?: number;
  status?: AppointmentStatus;
  type?: AppointmentType;
  notes?: string;
  cancellationReason?: string;
}

// Helper function to get organization ID
async function getOrganizationId(): Promise<string> {
  const userInfoCookie = cookies().get("user-info")?.value;
  
  if (userInfoCookie) {
    try {
      const userInfo = JSON.parse(userInfoCookie);
      return userInfo.organizationId || "";
    } catch (error) {
      console.error("Error parsing user info:", error);
    }
  }
  
  throw new Error("No organization found");
}

// Server Actions

/**
 * Get appointments with filters
 */
export async function getAppointments(filters: AppointmentFilters = {}) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "Unauthorized" };
    }

    const organizationId = await getOrganizationId();

    // Build the where clause
    let where: any = {
      organizationId,
    };

    if (filters.doctorId) {
      where.doctorId = filters.doctorId;
    }

    if (filters.patientId) {
      where.patientId = filters.patientId;
    }

    if (filters.branchId) {
      where.branchId = filters.branchId;
    }

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.date) {
      const selectedDate = new Date(filters.date);
      const nextDay = new Date(selectedDate);
      nextDay.setDate(nextDay.getDate() + 1);

      where.appointmentDate = {
        gte: selectedDate,
        lt: nextDay,
      };
    } else if (filters.startDate && filters.endDate) {
      where.appointmentDate = {
        gte: new Date(filters.startDate),
        lte: new Date(filters.endDate),
      };
    }

    if (filters.withoutConsultation) {
      where.consultation = null;
    }

    if (filters.forConsultation) {
      where.status = {
        in: ["scheduled", "confirmed", "in-consultation"],
      };
      where.consultation = null;
    }

    const appointments = await db.appointment.findMany({
      where,
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phone: true,
            email: true,
            dateOfBirth: true,
            gender: true,
            allergies: true,
          },
        },
        doctor: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            specialization: true,
            qualification: true,
            consultationFee: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
        queueStatus: true,
      },
      orderBy: [{ appointmentDate: "asc" }, { startTime: "asc" }],
    });

    return { success: true, data: appointments };
  } catch (error) {
    console.error("Error fetching appointments:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to fetch appointments" 
    };
  }
}

/**
 * Get single appointment by ID
 */
export async function getAppointment(id: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "Unauthorized" };
    }

    const appointment = await db.appointment.findUnique({
      where: { id },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phone: true,
            email: true,
            dateOfBirth: true,
            gender: true,
            allergies: true,
          },
        },
        doctor: {
          select: {
            id: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            specialization: true,
            qualification: true,
            consultationFee: true,
          },
        },
        branch: {
          select: {
            id: true,
            name: true,
          },
        },
        queueStatus: true,
      },
    });

    if (!appointment) {
      return { success: false, error: "Appointment not found" };
    }

    return { success: true, data: appointment };
  } catch (error) {
    console.error("Error fetching appointment:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to fetch appointment" 
    };
  }
}

/**
 * Create new appointment
 */
export async function createAppointment(data: CreateAppointmentData) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "Unauthorized" };
    }

    const organizationId = await getOrganizationId();

    // Validate required fields
    if (!data.patientId || !data.doctorId || !data.branchId || !data.appointmentDate || 
        !data.startTime || !data.endTime || !data.duration) {
      return { success: false, error: "Missing required fields" };
    }

    // Check for conflicts
    const conflictingAppointment = await db.appointment.findFirst({
      where: {
        doctorId: data.doctorId,
        appointmentDate: new Date(data.appointmentDate),
        startTime: data.startTime,
        status: {
          not: "cancelled",
        },
      },
    });

    if (conflictingAppointment) {
      return { success: false, error: "Doctor is not available at this time" };
    }

    const appointment = await db.appointment.create({
      data: {
        ...data,
        organizationId,
        appointmentDate: new Date(data.appointmentDate),
        status: "scheduled",
      },
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phone: true,
            email: true,
          },
        },
        doctor: {
          select: {
            id: true,
            user: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    revalidatePath("/appointments");
    return { success: true, data: appointment };
  } catch (error) {
    console.error("Error creating appointment:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create appointment"
    };
  }
}

/**
 * Update appointment
 */
export async function updateAppointment(id: string, data: UpdateAppointmentData) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "Unauthorized" };
    }

    // Check if appointment exists
    const existingAppointment = await db.appointment.findUnique({
      where: { id },
    });

    if (!existingAppointment) {
      return { success: false, error: "Appointment not found" };
    }

    // If updating time/date, check for conflicts
    if (data.doctorId || data.appointmentDate || data.startTime) {
      const doctorId = data.doctorId || existingAppointment.doctorId;
      const appointmentDate = data.appointmentDate ? new Date(data.appointmentDate) : existingAppointment.appointmentDate;
      const startTime = data.startTime || existingAppointment.startTime;

      const conflictingAppointment = await db.appointment.findFirst({
        where: {
          id: { not: id },
          doctorId,
          appointmentDate,
          startTime,
          status: { not: "cancelled" },
        },
      });

      if (conflictingAppointment) {
        return { success: false, error: "Doctor is not available at this time" };
      }
    }

    const updateData: any = { ...data };
    if (data.appointmentDate) {
      updateData.appointmentDate = new Date(data.appointmentDate);
    }

    const appointment = await db.appointment.update({
      where: { id },
      data: updateData,
      include: {
        patient: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            phone: true,
            email: true,
          },
        },
        doctor: {
          select: {
            id: true,
            user: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    revalidatePath("/appointments");
    revalidatePath(`/appointments/${id}`);
    return { success: true, data: appointment };
  } catch (error) {
    console.error("Error updating appointment:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update appointment"
    };
  }
}

/**
 * Delete appointment
 */
export async function deleteAppointment(id: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "Unauthorized" };
    }

    // Check if appointment exists
    const existingAppointment = await db.appointment.findUnique({
      where: { id },
      include: {
        queueStatus: true,
      },
    });

    if (!existingAppointment) {
      return { success: false, error: "Appointment not found" };
    }

    // Delete queue status if exists
    if (existingAppointment.queueStatus) {
      await db.queueStatus.delete({
        where: { id: existingAppointment.queueStatus.id },
      });
    }

    // Delete the appointment
    await db.appointment.delete({
      where: { id },
    });

    revalidatePath("/appointments");
    return { success: true, message: "Appointment deleted successfully" };
  } catch (error) {
    console.error("Error deleting appointment:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete appointment"
    };
  }
}

/**
 * Update queue status
 */
export async function updateQueueStatus(queueId: string, status: QueueStatusType, reason?: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "Unauthorized" };
    }

    const updateData: any = { status };

    if (reason) {
      updateData.pauseReason = reason;
    }

    // Add timestamps based on status
    if (status === "in-consultation") {
      updateData.actualStartTime = new Date();
    } else if (status === "completed") {
      updateData.completionTime = new Date();
    }

    const queueStatus = await db.queueStatus.update({
      where: { id: queueId },
      data: updateData,
    });

    revalidatePath("/appointments");
    return { success: true, data: queueStatus };
  } catch (error) {
    console.error("Error updating queue status:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update queue status"
    };
  }
}

/**
 * Search patients for appointment creation
 */
export async function searchPatients(query: string, branchId?: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "Unauthorized" };
    }

    const organizationId = await getOrganizationId();

    const where: any = {
      organizationId,
      OR: [
        { firstName: { contains: query, mode: "insensitive" } },
        { lastName: { contains: query, mode: "insensitive" } },
        { phone: { contains: query } },
        { email: { contains: query, mode: "insensitive" } },
      ],
    };

    if (branchId) {
      where.primaryBranchId = branchId;
    }

    const patients = await db.patient.findMany({
      where,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        phone: true,
        email: true,
        dateOfBirth: true,
        gender: true,
      },
      take: 20,
      orderBy: [{ firstName: "asc" }, { lastName: "asc" }],
    });

    // Format for combobox
    const formattedPatients = patients.map(patient => ({
      value: patient.id,
      label: `${patient.firstName} ${patient.lastName}`,
      description: `${patient.phone} • ${patient.gender}`,
      data: patient,
    }));

    return { success: true, data: formattedPatients };
  } catch (error) {
    console.error("Error searching patients:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to search patients"
    };
  }
}

/**
 * Search doctors for appointment creation
 */
export async function searchDoctors(query: string, branchId?: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "Unauthorized" };
    }

    const organizationId = await getOrganizationId();

    const where: any = {
      organizationId,
      user: {
        name: { contains: query, mode: "insensitive" },
      },
    };

    if (branchId) {
      where.branches.id = branchId;
    }

    const doctors = await db.doctor.findMany({
      where,
      select: {
        id: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        specialization: true,
        qualification: true,
        consultationFee: true,
      },
      take: 20,
      orderBy: { user: { name: "asc" } },
    });

    // Format for combobox
    const formattedDoctors = doctors.map(doctor => ({
      value: doctor.id,
      label: doctor.user.name,
      description: `${doctor.specialization} • ₹${doctor.consultationFee || 0}`,
      data: doctor,
    }));

    return { success: true, data: formattedDoctors };
  } catch (error) {
    console.error("Error searching doctors:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to search doctors"
    };
  }
}

/**
 * Get doctor details by ID
 */
export async function getDoctorDetails(doctorId: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "Unauthorized" };
    }

    const doctor = await db.doctor.findUnique({
      where: { id: doctorId },
      select: {
        id: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        specialization: true,
        qualification: true,
        consultationFee: true,
      },
    });

    if (!doctor) {
      return { success: false, error: "Doctor not found" };
    }

    return { success: true, data: doctor };
  } catch (error) {
    console.error("Error fetching doctor details:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch doctor details"
    };
  }
}

/**
 * Get available time slots
 */
export async function getTimeSlots(_doctorId: string, _date: string, query?: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, error: "Unauthorized" };
    }

    // This is a simplified implementation
    // In a real app, you'd check doctor schedules, existing appointments, etc.
    const timeSlots = [];

    // Generate time slots from 9 AM to 6 PM
    for (let hour = 9; hour < 18; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

        if (!query || time.includes(query)) {
          timeSlots.push({
            value: time,
            label: time,
            description: `${hour > 12 ? hour - 12 : hour}:${minute.toString().padStart(2, '0')} ${hour >= 12 ? 'PM' : 'AM'}`,
          });
        }
      }
    }

    return { success: true, data: timeSlots };
  } catch (error) {
    console.error("Error fetching time slots:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch time slots"
    };
  }
}
