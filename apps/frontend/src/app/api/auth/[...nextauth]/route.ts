export const maxDuration = 299;

import NextA<PERSON>, { AuthOptions, DefaultSession } from "next-auth";

// Extend the types
declare module "next-auth" {
  interface User {
    id: string;
    role: string;
    organizationId: string;
  }

  interface Session {
    user: {
      id: string;
      role: string;
      organizationId: string;
    } & DefaultSession["user"];
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    role: string;
    organizationId: string;
  }
}
import CredentialsProvider from "next-auth/providers/credentials";

// Define auth options separately but don't export it
const authOptions: AuthOptions = {
  debug: true,
  logger: {
    error(code, metadata) {
      console.error("NextAuth error:", { code, metadata });
    },
    warn(code) {
      console.warn("NextAuth warning:", code);
    },
    debug(code, metadata) {
      console.log("NextAuth debug:", { code, metadata });
    },
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // For demo purposes, hardcode the credentials check
          if (
            credentials.email === "<EMAIL>" &&
            credentials.password === "demo123"
          ) {
            return {
              id: "demo-user-id",
              email: "<EMAIL>",
              name: "Demo User",
              role: "user",
              organizationId: "org-1",
            };
          }

          if (
            credentials.email === "<EMAIL>" &&
            credentials.password === "admin123"
          ) {
            return {
              id: "admin-user-id",
              email: "<EMAIL>",
              name: "Admin User",
              role: "admin",
              organizationId: "org-1",
            };
          }
          return null;
        } catch (error) {
          console.error("Error in authorize function:", error);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.organizationId = user.organizationId;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id;
        session.user.role = token.role;
        session.user.organizationId = token.organizationId;
      }
      return session;
    },
  },
  pages: {
    signIn: "/sign-in",
    signOut: "/sign-in",
    error: "/sign-in",
  },
  session: {
    strategy: "jwt",
  },
  secret: process.env.NEXTAUTH_SECRET,
};

// Create the handler
const handler = NextAuth(authOptions);

// Export the handler functions
export const GET = handler;
export const POST = handler;
