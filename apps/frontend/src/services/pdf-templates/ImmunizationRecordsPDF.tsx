import React from "react";
import { Text, View, StyleSheet } from "@react-pdf/renderer";
import { format } from "date-fns";

// Helper function to format dates in IST timezone
const formatDateInIST = (date: Date | string, formatString: string = "PPP p") => {
  const dateObj = new Date(date);
  // Convert to IST (UTC+5:30)
  const istDate = new Date(dateObj.getTime() + (5.5 * 60 * 60 * 1000));
  return format(istDate, formatString) + " IST";
};
import { BasePDF } from "./BasePDF";
import {
  ConsultationData,
  PdfGenerationOptions,
} from "../pdf-generation/pdf-generator";

const styles = StyleSheet.create({
  section: { marginBottom: 20 },
  heading: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#000",
    paddingBottom: 4,
  },
  table: {
    width: "100%",
    borderWidth: 1,
    borderColor: "#000",
    borderStyle: "solid",
    marginBottom: 10,
  },
  tableRow: {
    flexDirection: "row",
  },
  tableColHeader: {
    width: "20%",
    borderStyle: "solid",
    borderBottomWidth: 1,
    borderRightWidth: 1,
    backgroundColor: "#f0f0f0",
    padding: 4,
    fontWeight: "bold",
    fontSize: 9,
  },
  tableCol: {
    width: "20%",
    borderStyle: "solid",
    borderBottomWidth: 1,
    borderRightWidth: 1,
    padding: 4,
    fontSize: 9,
  },
  detailSection: {
    marginBottom: 15,
    padding: 10,
    borderWidth: 1,
    borderColor: "#ddd",
    borderStyle: "solid",
  },
  detailRow: {
    flexDirection: "row",
    marginBottom: 3,
  },
  detailLabel: {
    fontSize: 9,
    fontWeight: "bold",
    width: "30%",
  },
  detailValue: {
    fontSize: 9,
    width: "70%",
  },
  notes: {
    fontSize: 10,
    marginTop: 10,
    lineHeight: 1.5,
  },
});

interface ImmunizationRecordsPDFProps {
  consultationData: ConsultationData & {
    immunizations?: any[];
  };
  options?: PdfGenerationOptions;
}

export const ImmunizationRecordsPDF: React.FC<ImmunizationRecordsPDFProps> = ({
  consultationData,
  options,
}) => {
  const { immunizations = [] } = consultationData;

  return (
    <BasePDF
      consultationData={consultationData}
      title="Immunization Records"
      options={options}
    >
      <View style={styles.section}>
        <Text style={styles.notes}>Total Records: {immunizations.length}</Text>
        <Text style={styles.notes}>
          Generated on: {formatDateInIST(new Date(), "PPP p")}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.heading}>Immunization Records</Text>
        {immunizations.length > 0 ? (
          immunizations.map((record: any, idx: number) => (
            <View key={idx} style={styles.detailSection}>
              {/* Record Header */}
              <View style={styles.detailRow}>
                <Text
                  style={[
                    styles.detailLabel,
                    { fontSize: 12, fontWeight: "bold" },
                  ]}
                >
                  Record #{idx + 1}
                </Text>
                <Text
                  style={[
                    styles.detailValue,
                    { fontSize: 12, fontWeight: "bold" },
                  ]}
                >
                  {record.vaccineDisplay || "Unknown Vaccine"}
                </Text>
              </View>

              {/* Patient Information */}
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Patient:</Text>
                <Text style={styles.detailValue}>
                  {record.patient?.firstName} {record.patient?.lastName}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Date Given:</Text>
                <Text style={styles.detailValue}>
                  {record.occurrenceDateTime
                    ? formatDateInIST(record.occurrenceDateTime, "PPP")
                    : "-"}
                </Text>
              </View>

              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Status:</Text>
                <Text style={styles.detailValue}>{record.status || "-"}</Text>
              </View>

              {/* Doctor Information */}
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Administered by:</Text>
                <Text style={styles.detailValue}>
                  {record.doctor?.user?.name || "Unknown Doctor"}
                </Text>
              </View>

              {/* Manufacturing Details */}
              {record.manufacturer && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Manufacturer:</Text>
                  <Text style={styles.detailValue}>{record.manufacturer}</Text>
                </View>
              )}

              {record.lotNumber && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Lot Number:</Text>
                  <Text style={styles.detailValue}>{record.lotNumber}</Text>
                </View>
              )}

              {record.expirationDate && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Expiration Date:</Text>
                  <Text style={styles.detailValue}>
                    {formatDateInIST(record.expirationDate, "PPP")}
                  </Text>
                </View>
              )}

              {/* Administration Details */}
              {record.site && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Site:</Text>
                  <Text style={styles.detailValue}>{record.site}</Text>
                </View>
              )}

              {record.route && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Route:</Text>
                  <Text style={styles.detailValue}>{record.route}</Text>
                </View>
              )}

              {record.doseQuantity && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Dose Number:</Text>
                  <Text style={styles.detailValue}>{record.doseQuantity}</Text>
                </View>
              )}

              {record.performer && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Performer:</Text>
                  <Text style={styles.detailValue}>{record.performer}</Text>
                </View>
              )}

              {/* Additional Information */}
              {record.location && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Location:</Text>
                  <Text style={styles.detailValue}>{record.location}</Text>
                </View>
              )}

              {record.reasonDisplay && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Reason:</Text>
                  <Text style={styles.detailValue}>{record.reasonDisplay}</Text>
                </View>
              )}

              {/* Protocol Applied */}
              {record.protocolApplied && record.protocolApplied.length > 0 && (
                <View>
                  <Text
                    style={[
                      styles.detailLabel,
                      { marginTop: 5, marginBottom: 3 },
                    ]}
                  >
                    Protocol Applied:
                  </Text>
                  {record.protocolApplied.map(
                    (protocol: any, protocolIdx: number) => (
                      <View key={protocolIdx} style={{ marginLeft: 10 }}>
                        {protocol.doseNumber && (
                          <View style={styles.detailRow}>
                            <Text style={styles.detailLabel}>Dose Number:</Text>
                            <Text style={styles.detailValue}>
                              {protocol.doseNumber}
                            </Text>
                          </View>
                        )}
                        {protocol.series && (
                          <View style={styles.detailRow}>
                            <Text style={styles.detailLabel}>Series:</Text>
                            <Text style={styles.detailValue}>
                              {protocol.series}
                            </Text>
                          </View>
                        )}
                        {protocol.targetDisease && (
                          <View style={styles.detailRow}>
                            <Text style={styles.detailLabel}>
                              Target Disease:
                            </Text>
                            <Text style={styles.detailValue}>
                              {Array.isArray(protocol.targetDisease)
                                ? protocol.targetDisease.join(", ")
                                : protocol.targetDisease}
                            </Text>
                          </View>
                        )}
                      </View>
                    ),
                  )}
                </View>
              )}

              {/* Notes */}
              {record.note && (
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Notes:</Text>
                  <Text style={styles.detailValue}>{record.note}</Text>
                </View>
              )}

              {/* Reactions */}
              {record.reaction && record.reaction.length > 0 && (
                <View>
                  <Text
                    style={[
                      styles.detailLabel,
                      { marginTop: 5, marginBottom: 3 },
                    ]}
                  >
                    Reactions:
                  </Text>
                  {record.reaction.map((reaction: any, reactionIdx: number) => (
                    <View key={reactionIdx} style={{ marginLeft: 10 }}>
                      {reaction.detail && (
                        <View style={styles.detailRow}>
                          <Text style={styles.detailLabel}>Detail:</Text>
                          <Text style={styles.detailValue}>
                            {reaction.detail}
                          </Text>
                        </View>
                      )}
                      {reaction.date && (
                        <View style={styles.detailRow}>
                          <Text style={styles.detailLabel}>Date:</Text>
                          <Text style={styles.detailValue}>
                            {format(new Date(reaction.date), "PPP")}
                          </Text>
                        </View>
                      )}
                    </View>
                  ))}
                </View>
              )}
            </View>
          ))
        ) : (
          <Text style={styles.notes}>No immunization records found.</Text>
        )}
      </View>
    </BasePDF>
  );
};
