{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**"]}, "lint": {}, "dev": {"cache": false, "persistent": true}}, "globalEnv": ["DATABASE_URL", "NEXTAUTH_SECRET", "NEXTAUTH_URL", "NEXT_PUBLIC_ABDM_CLIENT_ID", "NEXT_PUBLIC_ABDM_CLIENT_SECRET", "ABDM_HIP_ID", "ABDM_CM_ID", "ABDM_PUBLIC_KEY", "S3_ACCESS_KEY", "S3_BUCKET_NAME", "S3_SECRET_KEY", "NEXT_PUBLIC_AZURE_STORAGE_CONNECTION_STRING", "NEXT_PUBLIC_AZURE_STORAGE_CONTAINER_NAME", "NEXT_PUBLIC_AZURE_STORAGE_ACCOUNT_NAME", "AZURE_STORAGE_SAS_TOKEN", "TWILIO_ACCOUNT_SID", "TWILIO_AUTH_TOKEN", "TWILIO_PHONE_NUMBER"]}